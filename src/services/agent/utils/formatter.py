"""
事件格式化实用程序，用于处理agent输出事件
"""
import json
from src.utils.logger import logger
from agents import ItemHelpers, StreamEvent, RunItem


# 缓存已知的agent名称，避免重复获取
_KNOWN_AGENTS_CACHE = None

def _get_known_agents() -> list:
    """
    获取当前配置的所有可用agent名称（带缓存）

    Returns:
        list: 包含所有可用agent名称的列表
    """
    global _KNOWN_AGENTS_CACHE

    if _KNOWN_AGENTS_CACHE is not None:
        return _KNOWN_AGENTS_CACHE

    try:
        from src.services.agent.agent_service import get_agent_names
        all_agents = get_agent_names()
        _KNOWN_AGENTS_CACHE = list(set(all_agents))
        logger.debug(f"缓存agent名称列表: {_KNOWN_AGENTS_CACHE}")
        return _KNOWN_AGENTS_CACHE

    except Exception as e:
        logger.warning(f"获取agent列表失败，使用默认列表: {e}")
        # 默认列表
        _KNOWN_AGENTS_CACHE = [
            'sales_order_analytics', 'sales_kpi_analytics',
            'warehouse_and_fulfillment', 'general_chat_bot',
            'coordinator_bot', 'catering_expert', 'sale_bd_manage_merchat'
        ]
        return _KNOWN_AGENTS_CACHE


def _extract_agent_name_from_tool_call(tool_call_item:RunItem) -> str:
    """
    从工具调用项中提取agent名称（简化版本）

    Args:
        tool_call_item: 工具调用项对象

    Returns:
        str: 提取的agent名称，如果无法提取则返回None
    """
    try:
        known_agents = _get_known_agents()
        
        raw_item = tool_call_item.raw_item
        agent_name = raw_item.name if hasattr(raw_item, 'name') else None

        # 简化：直接从raw_item的name属性获取
        if agent_name and agent_name in known_agents:
            return agent_name

        return None

    except Exception as e:
        logger.exception(f"提取agent名称时出错: {e}")
        return None


def format_event_message(event: StreamEvent) -> list[dict]:
    """
    将事件格式化为可读的消息字符串，并以JSON对象返回。

    Args:
        event: 要格式化的事件对象

    Returns:
        list[dict]: 格式化后的消息字典列表
    """
    try:
        if (
                event.type == "raw_response_event"
                and hasattr(event.data, "delta")
                and event.data.delta
        ):
            return _handle_raw_response_event(event)
        elif event.type == "agent_thinking_event":
            return _handle_agent_thinking_event(event)
        elif event.type == "run_item_stream_event":
            return _handle_run_item_stream_event(event)
        elif event.type == "agent_updated_stream_event":
            logger.info(f"agent_updated_stream_event: {event.new_agent.name}")
            return _handle_agent_updated_stream_event(event)
        else:
            # logger.warning(f"Unknown event: {event}")
            return []
    except Exception as e:
        logger.exception(f"格式化事件时出错: {e}, 事件: {event}")
        return [{"type": "log", "content": str(e)+str(event)+"\n", "error": True}]

def _handle_agent_updated_stream_event(event: StreamEvent) -> list[dict]:
    """处理agent更新事件，返回handoff日志"""
    new_agent = event.new_agent.name if event.new_agent else "unknown_agent"
    return [{"type": "handoff_log", "content": new_agent}]
    

def _handle_raw_response_event(event: StreamEvent) -> list[dict]:
    """处理原始响应事件，返回数据和日志消息"""
    return [{"type": "data", "content": event.data.delta},{"type": "log", "content": event.data.delta}]


def _handle_agent_thinking_event(event: StreamEvent) -> list[dict]:
    """处理agent思考事件，返回思考消息"""
    return [{"type": "thinking", "content": event.data.delta}]


def _handle_run_item_stream_event(event: StreamEvent) -> list[dict]:
    """处理运行项流事件，根据不同类型返回相应的消息列表"""
    if event.item.type == "tool_call_item":
        # 提取agent名称并生成handoff_log
        agent_name = _extract_agent_name_from_tool_call(event.item)
        args = event.item.raw_item.arguments if hasattr(event.item.raw_item, 'arguments') else ''
        args_dict = json.loads(args) if isinstance(args, str) else args
        # 这三个字段是工具调用的参数，如果存在，则返回，以告知用户AI正在使用工具
        messages_to_emit=[]
        tool_call_args = args_dict.get("input", args_dict.get("description", args_dict.get("product_name")))
        if tool_call_args:
            messages_to_emit.append({"type": "data", "content": f"\n___\n{tool_call_args}\n"})
        else:
            loggerable_args = "".join([f"- {key}: {value}\n" for key, value in args_dict.items()])
            messages_to_emit.append({"type": "log", "content": loggerable_args + "\n___\n",})
            messages_to_emit.append({"type": "data", "content": "\n___\n" + loggerable_args[-200:]})
        if agent_name:
            messages_to_emit.append({"type": "handoff_log", "content": f"{agent_name}"})
        messages_to_emit.append({"type": "tool_call_log", "content": f"{event.item.raw_item if hasattr(event.item, 'raw_item') else event.item}\n\n",})
        return messages_to_emit
    elif event.item.type == "tool_call_output_item":
        return _handle_tool_call_output_item(event)
    elif event.item.type == "handoff_output_item":
        target_agent = getattr(event.item, 'target_agent', None)

        if target_agent and hasattr(target_agent, 'name'):
            content = target_agent.name
        else:
            content = "unknown_agent"

        return [{"type": "handoff_log", "content": content}]
    elif event.item.type == "message_output_item":
        return [{"type": "final_response", "content": f"\n___\n{ItemHelpers.text_message_output(event.item)}"}]

    # 如果没有匹配的类型，返回空列表
    return []


def _handle_tool_call_output_item(event: StreamEvent) -> list[dict]:
    """处理工具调用输出项，返回相应的消息列表"""
    output = str(event.item.output)
    is_ask_user_to_confirm_sku = "question_to_user" in output

    if is_ask_user_to_confirm_sku:
        return [{
            "type": "confirm_sku",
            "content": output,
        }]

    is_ddl_read_operator = "CREATE TABLE" in output or "TABLE_SCHEMA" in output
    return [{
        "type": "tool_output",
        "content": ("AI读取了DDL文件" if is_ddl_read_operator else output)+"\n\n",
    }]
