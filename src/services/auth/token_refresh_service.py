"""
定时Token刷新服务

负责定时检查并刷新即将过期的用户access token
"""

import threading
import requests
from datetime import datetime, timedelta
from typing import Optional, Tuple

from src.services.auth.user_session_service import user_session_service
from src.services.auth.user_login_with_feishu import APP_ID, APP_SECRET
from src.utils.logger import logger


class TokenRefreshService:
    """定时Token刷新服务类"""

    def __init__(self, check_interval_minutes=5, expiry_threshold_minutes=10):
        """
        初始化刷新服务

        Args:
            check_interval_minutes: 检查间隔（分钟），默认5分钟
            expiry_threshold_minutes: 过期阈值（分钟），默认10分钟
        """
        self.check_interval_minutes = check_interval_minutes
        self.expiry_threshold_minutes = expiry_threshold_minutes
        self.refresh_thread = None
        self.is_running = False
        self.stop_event = threading.Event()
    
    def start(self):
        """启动刷新服务"""
        if self.is_running:
            logger.warning("Token刷新服务已在运行中")
            return

        self.is_running = True
        self.stop_event.clear()  # 重置停止事件
        self.refresh_thread = threading.Thread(target=self._refresh_loop, daemon=True)
        self.refresh_thread.start()
        logger.info(f"Token刷新服务已启动，检查间隔: {self.check_interval_minutes}分钟，过期阈值: {self.expiry_threshold_minutes}分钟")

    def stop(self):
        """停止刷新服务"""
        if not self.is_running:
            logger.warning("Token刷新服务未在运行")
            return

        self.is_running = False
        self.stop_event.set()  # 设置停止事件
        
        if self.refresh_thread:
            # 等待线程结束，最多等待30秒
            self.refresh_thread.join(timeout=30)
            if self.refresh_thread.is_alive():
                logger.warning("Token刷新服务线程未能在30秒内正常结束")
            else:
                logger.info("Token刷新服务已停止")
    
    def _refresh_loop(self):
        """刷新循环"""
        while self.is_running:
            try:
                # 执行token刷新检查
                refreshed_count = self._check_and_refresh_tokens()
                if refreshed_count > 0:
                    logger.info(f"Token刷新任务执行完成，刷新了 {refreshed_count} 个token")

                # 使用Event.wait()替代循环睡眠，支持快速中断
                if self.stop_event.wait(timeout=self.check_interval_minutes * 60):
                    # 如果stop_event被设置，立即退出循环
                    break

            except (SystemExit, KeyboardInterrupt):
                # 不捕获系统退出和键盘中断信号，让它们正常传播
                raise
            except Exception as e:
                logger.exception(f"Token刷新任务执行失败: {e}", exc_info=True)
                # 出错后等待1分钟再重试，同样支持快速中断
                if self.stop_event.wait(timeout=60):
                    break

    def _check_and_refresh_tokens(self) -> int:
        """
        检查并刷新即将过期的tokens
        
        Returns:
            int: 成功刷新的token数量
        """
        try:
            # 获取即将过期的sessions
            sessions_to_refresh = user_session_service.get_sessions_near_expiry(self.expiry_threshold_minutes)
            
            if not sessions_to_refresh:
                logger.info("没有需要刷新的token")
                return 0
            
            logger.info(f"发现 {len(sessions_to_refresh)} 个即将过期的token，开始刷新")
            
            refreshed_count = 0
            for session in sessions_to_refresh:
                try:
                    # 刷新单个session的token
                    if self._refresh_session_token(session.session_id, session.refresh_token, session.open_id):
                        refreshed_count += 1
                        logger.info(f"成功刷新token: session_id={session.session_id}, open_id={session.open_id}")
                    else:
                        logger.warning(f"刷新token失败: session_id={session.session_id}, open_id={session.open_id}")

                except Exception as e:
                    logger.exception(f"刷新session token时出错: session_id={session.session_id}, error={e}", exc_info=True)
                    
            return refreshed_count
            
        except Exception as e:
            logger.exception(f"检查和刷新tokens失败: {e}", exc_info=True)
            return 0

    def _refresh_session_token(self, session_id: str, refresh_token: str, open_id: str) -> bool:
        """
        刷新单个session的token

        Args:
            session_id: session ID
            refresh_token: 用于刷新的refresh token
            open_id: 用户的open_id

        Returns:
            bool: 是否刷新成功
        """
        try:
            # 调用飞书API刷新token
            new_access_token, new_refresh_token = self._call_feishu_refresh_api(refresh_token)

            if new_access_token:
                # 计算新的过期时间（飞书access token通常2小时过期）
                expires_at = datetime.now() + timedelta(hours=2)

                # 更新session中的token信息
                success = user_session_service.update_session_tokens(
                    session_id,
                    new_access_token,
                    new_refresh_token or refresh_token,  # 如果没有返回新的refresh token，使用原来的
                    expires_at
                )

                if success:
                    logger.info(f"Token刷新成功: session_id={session_id}")

                    # 在token刷新成功后，尝试更新用户的一级部门信息
                    try:
                        # 获取session对应的open_id
                        session = user_session_service.get_session_by_id(session_id)
                        if session and session.open_id:
                            self._update_user_department(session.open_id)
                        else:
                            logger.warning(f"无法获取session信息来更新用户部门: session_id={session_id}")
                    except Exception as e:
                        # 部门更新失败不应该影响token刷新的主要功能
                        logger.exception(f"更新用户部门信息时出错: session_id={session_id}, error={e}", exc_info=True)

                    return True
                else:
                    logger.exception(f"更新session中的token信息失败: session_id={session_id}")
                    return False
            else:
                logger.exception(f"调用飞书API刷新token失败: session_id={session_id}")

                # token刷新失败时，检查用户是否已离职
                try:
                    self._handle_token_refresh_failure(session_id, open_id)
                except Exception as e:
                    logger.exception(f"处理token刷新失败时出错: session_id={session_id}, open_id={open_id}, error={e}", exc_info=True)

                return False
                
        except Exception as e:
            logger.exception(f"刷新session token失败: session_id={session_id}, error={e}", exc_info=True)
            return False

    def _call_feishu_refresh_api(self, refresh_token: str) -> Tuple[Optional[str], Optional[str]]:
        """
        调用飞书API刷新token
        
        Args:
            refresh_token: 用于刷新的refresh token
            
        Returns:
            Tuple[Optional[str], Optional[str]]: (新的access_token, 新的refresh_token)
        """
        token_url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token"
        payload = {
            "grant_type": "refresh_token",
            "client_id": APP_ID,
            "client_secret": APP_SECRET,
            "refresh_token": refresh_token,
        }
        
        try:
            response = requests.post(token_url, json=payload, timeout=10)
            result = response.json()
            
            if response.status_code == 200 and "access_token" in result:
                new_access_token = result["access_token"]
                new_refresh_token = result.get("refresh_token", refresh_token)
                logger.info(f"飞书token刷新API调用成功")
                return new_access_token, new_refresh_token
            else:
                logger.exception(f"飞书token刷新API调用失败: status={response.status_code}, result={result}")
                
                # 如果是refresh token失效，记录警告
                if result.get("code", 0) == 20064:
                    logger.warning(f"refresh token已失效，需要用户重新登录")
                
                return None, None
                
        except Exception as e:
            logger.exception(f"调用飞书token刷新API时出错: {e}", exc_info=True)
            return None, None

    def get_status(self) -> dict:
        """
        获取服务状态
        
        Returns:
            dict: 服务状态信息
        """
        return {
            "is_running": self.is_running,
            "check_interval_minutes": self.check_interval_minutes,
            "expiry_threshold_minutes": self.expiry_threshold_minutes,
            "thread_alive": self.refresh_thread.is_alive() if self.refresh_thread else False
        }

    def _update_user_department(self, open_id: str):
        """
        更新用户的一级部门信息

        Args:
            open_id: 用户的open_id
        """
        try:
            from src.services.feishu.user_service import UserService
            success = UserService.update_user_first_level_department(open_id)

            if success:
                logger.info(f"成功更新用户一级部门: open_id={open_id}")
            else:
                logger.warning(f"更新用户一级部门失败: open_id={open_id}")

        except Exception as e:
            logger.exception(f"更新用户一级部门时出错: open_id={open_id}, error={e}", exc_info=True)

    def _handle_token_refresh_failure(self, session_id: str, open_id: str):
        """
        处理token刷新失败的情况

        检查用户是否已离职，如果已离职则更新session和用户状态

        Args:
            session_id: session ID
            open_id: 用户的open_id
        """
        try:
            logger.info(f"开始处理token刷新失败: session_id={session_id}, open_id={open_id}")

            # 检查用户是否已离职
            from src.services.feishu.department_service import DepartmentService
            is_resigned = DepartmentService.check_user_resigned(open_id)

            if is_resigned:
                logger.warning(f"检测到用户已离职，开始更新状态: session_id={session_id}, open_id={open_id}")

                # 停用session
                success_session = user_session_service.invalidate_session(session_id)
                if success_session:
                    logger.info(f"已停用离职用户的session: session_id={session_id}, open_id={open_id}")
                else:
                    logger.exception(f"停用离职用户session失败: session_id={session_id}, open_id={open_id}")

                # 更新用户离职状态
                from src.repositories.chatbi.user import ChatbiUserRepository
                user_repo = ChatbiUserRepository()
                success_user = user_repo.update_user_resigned_status(open_id, True)
                if success_user:
                    logger.info(f"已更新用户离职状态: open_id={open_id}")
                else:
                    logger.exception(f"更新用户离职状态失败: open_id={open_id}")

            else:
                logger.info(f"用户未离职，token刷新失败可能由其他原因导致: session_id={session_id}, open_id={open_id}")

        except Exception as e:
            logger.exception(f"处理token刷新失败时出错: session_id={session_id}, open_id={open_id}, error={e}", exc_info=True)


# 全局刷新服务实例
token_refresh_service = TokenRefreshService()


def start_token_refresh_service():
    """启动Token刷新服务"""
    token_refresh_service.start()


def stop_token_refresh_service():
    """停止Token刷新服务"""
    token_refresh_service.stop()
